<script>
  import { game, handleCommand } from "./game.js";
  import { onMount, afterUpdate } from "svelte";

  let input = "";
  let lines = [
    `Willkommen zu: ${game.title}`,
    "",
    game.look()
  ];

  let container;
  let inputField;

  function sendCommand() {
    if (!input.trim()) return;
    lines.push(`> ${input}`);
    lines.push(handleCommand(game, input));
    input = "";
  }

  // Automatisch scrollen
  afterUpdate(() => {
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
    // Cursor zurück ins Eingabefeld
    if (inputField) {
      inputField.focus();
    }
  });

  // Beim <PERSON>okus setzen
  onMount(() => {
    if (inputField) inputField.focus();
  });
</script>

<main class="h-screen bg-black text-gray-100 font-mono flex flex-col">
  <!-- Textfenster -->
  <div
    bind:this={container}
    class="flex-1 overflow-y-auto p-4 whitespace-pre-wrap"
  >
    {#each lines as line}
      <div>{@html line}</div>
    {/each}

    <!-- Eingabezeile -->
    <div class="flex">
      <span class="text-green-400 mr-2">&gt;</span>
      <input
        bind:value={input}
        bind:this={inputField}
        on:keydown={(e) => e.key === "Enter" && sendCommand()}
        class="flex-1 bg-transparent outline-none"
      />
    </div>
  </div>
</main>
