<script>
  import { game, handleCommand } from "./game.js";
  import { onMount, afterUpdate } from "svelte";

  let input = "";
  let lines = [
    `Willkommen zu: ${game.title}`,
    "",
    game.look(),
    ""
  ];

  let container;
  let inputField;

  function sendCommand() {
    if (!input.trim()) return;

    // Add the command to the display
    lines.push(`> ${input}`);

    // Process the command and add the response
    const response = handleCommand(game, input);
    lines.push(response);
    lines.push(""); // Add empty line for spacing

    // Clear input and trigger reactivity
    input = "";
    lines = lines; // Force Svelte to update
  }

  function handleKeydown(e) {
    if (e.key === "Enter") {
      sendCommand();
    }
  }

  // Automatisch scrollen
  afterUpdate(() => {
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
    // Cursor zurück ins Eingabefeld
    if (inputField) {
      inputField.focus();
    }
  });

  // <PERSON><PERSON>okus setzen
  onMount(() => {
    if (inputField) inputField.focus();
  });
</script>

<main class="h-screen bg-black text-green-400 font-mono p-4 flex flex-col">
  <!-- Terminal Window -->
  <div class="flex-1 flex flex-col border border-green-400 rounded">
    <!-- Terminal Header -->
    <div class="bg-green-400 text-black px-3 py-1 text-sm font-bold">
      TEXT ADVENTURE TERMINAL
    </div>

    <!-- Terminal Content -->
    <div
      bind:this={container}
      class="flex-1 overflow-y-auto p-3 whitespace-pre-wrap"
    >
      {#each lines as line}
        <div>{@html line}</div>
      {/each}

      <!-- Current input line -->
      <div class="flex">
        <span class="text-green-400 mr-1">&gt;</span>
        <input
          bind:value={input}
          bind:this={inputField}
          on:keydown={handleKeydown}
          class="flex-1 bg-transparent outline-none text-green-400 caret-green-400"
          autocomplete="off"
          spellcheck="false"
        />
        <span class="animate-pulse">█</span>
      </div>
    </div>
  </div>
</main>
