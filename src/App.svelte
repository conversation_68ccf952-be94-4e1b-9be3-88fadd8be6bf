<script>
  import { game, handleCommand } from "./game.js";
  import { onMount, afterUpdate } from "svelte";

  let input = "";
  let lines = [
    `<PERSON>kommen zu: ${game.title}`,
    "",
    game.look(),
    ""
  ];

  let container;

  function sendCommand() {
    if (!input.trim()) return;

    // Add the command to the display
    lines.push(`> ${input}`);

    // Process the command and add the response
    const response = handleCommand(game, input);
    lines.push(response);
    lines.push(""); // Add empty line for spacing

    // Clear input and trigger reactivity
    input = "";
    lines = lines; // Force Svelte to update
  }

  function handleKeydown(e) {
    if (e.key === "Enter") {
      sendCommand();
    } else if (e.key === "Backspace") {
      input = input.slice(0, -1);
    } else if (e.key.length === 1) {
      input += e.key;
    }
  }

  // Automatisch scrollen
  afterUpdate(() => {
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  });

  // Focus the window to capture keystrokes
  onMount(() => {
    window.addEventListener('keydown', handleKeydown);
    return () => {
      window.removeEventListener('keydown', handleKeydown);
    };
  });
</script>

<main class="terminal">
  <div bind:this={container} class="terminal-content">
    {#each lines as line}
      <div>{@html line}</div>
    {/each}

    <div class="input-line">
      <span>&gt; {input}<span class="cursor">█</span></span>
    </div>
  </div>
</main>

<style>
  .terminal {
    height: 100vh;
    background-color: #000000;
    color: #00ff00;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.4;
    overflow: hidden;
    margin: 0;
    padding: 0;
  }

  .terminal-content {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    white-space: pre-wrap;
    text-align: left;
  }

  .terminal-content div {
    margin: 0;
    padding: 0;
    color: #00ff00;
    font-family: inherit;
  }

  .input-line {
    margin: 0;
    padding: 0;
  }

  .cursor {
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }

  /* Ensure all HTML content is green and monospaced */
  :global(.terminal-content *) {
    color: #00ff00 !important;
    font-family: 'Courier New', Courier, monospace !important;
  }
</style>
