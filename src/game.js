import yaml from "js-yaml";
import gameData from "./game.yaml?raw";

const data = yaml.load(gameData);

function highlight(text, color) {
  return `<span class="text-${color}-400">${text}</span>`;
}

class GameObject {
  constructor(data) {
    this.name = data.name.toLowerCase();
    this.weight = data.weight ?? 0.0;
    this.canBePickedUp = data.can_be_picked_up ?? false;
    this.description = data.description ?? "";
    this.details = {};
    (data.details ?? []).forEach(d => {
      this.details[d.key.toLowerCase()] = d.text;
    });
    this.position = data.position ?? "";
    this.location = data.location ?? "";
  }

  examine() {
    let desc = this.description + "<br/>";
    if (Object.keys(this.details).length > 0) {
      desc += "Details:<br/>";
      for (const [k, v] of Object.entries(this.details)) {
        desc += ` - ${highlight(k, "yellow")}: ${v}<br/>`;
      }
    }
    return desc;
  }
}

class Location {
  constructor(name, data) {
    this.name = name;
    this.description = data.description;
    this.exits = data.exits ?? {};
    this.objectNames = (data.objects ?? []).map(o => o.toLowerCase());
    this.objects = []; // später gefüllt
  }
}

export class Game {
  constructor(data) {
    this.title = data.game.title;
    this.startLocation = data.game.start_location;

    this.locations = {};
    for (const [name, locData] of Object.entries(data.locations)) {
      this.locations[name] = new Location(name, locData);
    }

    this.objects = {};
    for (const [name, objData] of Object.entries(data.objects)) {
      this.objects[name.toLowerCase()] = new GameObject(objData);
    }

    // Objekte zu Räumen hinzufügen
    for (const loc of Object.values(this.locations)) {
      loc.objects = loc.objectNames.map(n => this.objects[n]);
    }

    this.currentLocation = this.locations[this.startLocation];
    this.inventory = [];
  }

  look() {
    let desc = highlight(this.currentLocation.name, "green") + "<br/>";
    desc += this.currentLocation.description + "<br/>";
    if (this.currentLocation.objects.length > 0) {
      desc += "Du siehst hier:<br/>";
      this.currentLocation.objects.forEach(o => {
        desc += ` - ${highlight(o.name, "yellow")}<br/>`;
      });
    }
    if (Object.keys(this.currentLocation.exits).length > 0) {
      desc +=
        "Ausgänge: " +
        Object.keys(this.currentLocation.exits)
          .map(e => highlight(e, "blue"))
          .join(", ");
    }
    return desc;
  }

  go(direction) {
    if (this.currentLocation.exits[direction]) {
      const newLocName = this.currentLocation.exits[direction];
      this.currentLocation = this.locations[newLocName];
      return `Du gehst nach ${highlight(direction, "blue")}.<br/>` + this.look();
    }
    return "Dort kannst du nicht hingehen.";
  }

  take(objName) {
    objName = objName.toLowerCase();
    const obj = this.currentLocation.objects.find(o => o.name === objName);
    if (!obj) return `Hier gibt es keinen ${highlight(objName, "yellow")}.`;
    if (!obj.canBePickedUp)
      return `Du kannst den ${highlight(obj.name, "yellow")} nicht mitnehmen.`;

    this.inventory.push(obj);
    this.currentLocation.objects = this.currentLocation.objects.filter(
      o => o !== obj
    );
    return `Du nimmst den ${highlight(obj.name, "yellow")}.`;
  }

  inventoryList() {
    if (this.inventory.length === 0) return "Du hast nichts dabei.";
    return (
      "Du trägst:<br/>" +
      this.inventory.map(o => ` - ${highlight(o.name, "yellow")}`).join("<br/>")
    );
  }

  examine(objName) {
    objName = objName.toLowerCase();
    const obj =
      this.currentLocation.objects.find(o => o.name === objName) ||
      this.inventory.find(o => o.name === objName);
    if (!obj) return `Kein ${highlight(objName, "yellow")} hier.`;
    return obj.examine();
  }
}

export function handleCommand(game, command) {
  const tokens = command.trim().toLowerCase().split(/\s+/);
  if (tokens.length === 0) return "Bitte gib einen Befehl ein.";
  const verb = tokens[0];

  if (["schau", "look", "umsehen"].includes(verb)) {
    return game.look();
  } else if (["gehe", "go"].includes(verb) && tokens[1]) {
    return game.go(tokens[1]);
  } else if (["nimm", "take"].includes(verb) && tokens[1]) {
    return game.take(tokens.slice(1).join(" "));
  } else if (["inventar", "inventory"].includes(verb)) {
    return game.inventoryList();
  } else if (["untersuche", "examine"].includes(verb) && tokens[1]) {
    return game.examine(tokens.slice(1).join(" "));
  }
  return "Das verstehe ich nicht.";
}

export const game = new Game(data);
